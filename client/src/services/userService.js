import { LocalStorage } from "../lib/storage.js";
import { generateUUID } from "../lib/uuid.js";
import { markCriterionComplete } from "../lib/criteria.js";

export function registerUser(userData) {
  // Criteria 3: Objects as data records
  markCriterionComplete(3, "Objects as Data Records");
  
  // Criteria 13: File I/O via localStorage
  markCriterionComplete(13, "File I/O");

  const users = LocalStorage.getUsers();
  
  // Check if email already exists
  const existingUser = users.find(u => u.email === userData.email);
  if (existingUser) {
    throw new Error('Email already exists');
  }

  const user = {
    id: generateUUID(),
    name: userData.name,
    email: userData.email,
    passwordHash: userData.password, // In real app, this would be hashed
    role: userData.role,
    blocked: false,
    createdAt: Date.now(),
  };

  users.push(user);
  LocalStorage.saveUsers(users);
  
  return user;
}

export function loginUser(email, password) {
  // Criteria 15: Use of sentinels or flags
  markCriterionComplete(15, "Sentinels or Flags");

  const users = LocalStorage.getUsers();
  const user = users.find(u => u.email === email && u.passwordHash === password);
  
  if (user && !user.blocked) {
    LocalStorage.saveCurrentUser(user.id);
    return user;
  }
  
  return null;
}

export function getCurrentUser() {
  const currentUserId = LocalStorage.getCurrentUser();
  if (!currentUserId) return null;
  
  const users = LocalStorage.getUsers();
  return users.find(u => u.id === currentUserId) || null;
}

export function getAllUsers() {
  // Criteria 1: Arrays
  markCriterionComplete(1, "Arrays");
  
  return LocalStorage.getUsers();
}

export function updateUser(userId, updates) {
  const users = LocalStorage.getUsers();
  const userIndex = users.findIndex(u => u.id === userId);
  
  if (userIndex === -1) return null;
  
  const updatedUser = { ...users[userIndex], ...updates };
  users[userIndex] = updatedUser;
  LocalStorage.saveUsers(users);
  
  return updatedUser;
}

export function blockUser(userId) {
  const user = updateUser(userId, { blocked: true });
  return user !== null;
}

export function deleteUser(userId) {
  const users = LocalStorage.getUsers();
  const filteredUsers = users.filter(u => u.id !== userId);
  
  if (filteredUsers.length < users.length) {
    LocalStorage.saveUsers(filteredUsers);
    return true;
  }
  
  return false;
}

export function logout() {
  LocalStorage.saveCurrentUser(null);
}