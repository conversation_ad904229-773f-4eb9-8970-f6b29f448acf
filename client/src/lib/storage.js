const STORAGE_KEYS = {
  USERS: 'bookstore_users',
  BOOKS: 'bookstore_books', 
  ORDERS: 'bookstore_orders',
  CRITERIA_FLAGS: 'bookstore_criteria_flags',
  CURRENT_USER: 'bookstore_current_user',
  CART: 'bookstore_cart',
};

export class LocalStorage {
  static getUsers() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.USERS);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  static saveUsers(users) {
    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
  }

  static getBooks() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.BOOKS);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  static saveBooks(books) {
    localStorage.setItem(STORAGE_KEYS.BOOKS, JSON.stringify(books));
  }

  static getOrders() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.ORDERS);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  static saveOrders(orders) {
    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));
  }

  static getCriteriaFlags() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.CRITERIA_FLAGS);
      return data ? JSON.parse(data) : {
        criterion1: false, criterion2: false, criterion3: false, criterion4: false, criterion5: false,
        criterion6: false, criterion7: false, criterion8: false, criterion9: false, criterion10: false,
        criterion11: false, criterion12: false, criterion13: false, criterion14: false, criterion15: false,
      };
    } catch {
      return {
        criterion1: false, criterion2: false, criterion3: false, criterion4: false, criterion5: false,
        criterion6: false, criterion7: false, criterion8: false, criterion9: false, criterion10: false,
        criterion11: false, criterion12: false, criterion13: false, criterion14: false, criterion15: false,
      };
    }
  }

  static saveCriteriaFlags(flags) {
    localStorage.setItem(STORAGE_KEYS.CRITERIA_FLAGS, JSON.stringify(flags));
  }

  static getCurrentUser() {
    return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);
  }

  static saveCurrentUser(userId) {
    if (userId) {
      localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);
    } else {
      localStorage.removeItem(STORAGE_KEYS.CURRENT_USER);
    }
  }

  static getCart() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.CART);
      return data ? JSON.parse(data) : { items: [], total: 0 };
    } catch {
      return { items: [], total: 0 };
    }
  }

  static saveCart(cart) {
    localStorage.setItem(STORAGE_KEYS.CART, JSON.stringify(cart));
  }

  static clear() {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }
}