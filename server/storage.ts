import { randomUUID } from "crypto";
import type { User, InsertUser, Book, InsertBook, Order, InsertOrder } from "../shared/schema.ts";

export class MemStorage {
  private users: Map<string, User>;
  private books: Map<string, Book>;
  private orders: Map<string, Order>;

  constructor() {
    this.users = new Map();
    this.books = new Map();
    this.orders = new Map();
    this.seedData();
  }

  seedData() {
    // Create admin user
    const adminId = randomUUID();
    const admin: User = {
      id: adminId,
      name: "Admin User",
      email: "<EMAIL>",
      passwordHash: "Admin@123", // In real app, this would be hashed
      role: "admin",
      blocked: false,
      createdAt: Date.now(),
    };
    this.users.set(adminId, admin);

    // Create sample seller
    const sellerId = randomUUID();
    const seller: User = {
      id: sellerId,
      name: "<PERSON>",
      email: "<EMAIL>",
      passwordHash: "password123",
      role: "seller",
      blocked: false,
      createdAt: Date.now(),
    };
    this.users.set(sellerId, seller);

    // Create sample buyer
    const buyerId = randomUUID();
    const buyer: User = {
      id: buyerId,
      name: "<PERSON>",
      email: "<EMAIL>",
      passwordHash: "password123",
      role: "buyer",
      blocked: false,
      createdAt: Date.now(),
    };
    this.users.set(buyerId, buyer);

    // Create sample books
    const books: Book[] = [
      {
        id: randomUUID(),
        title: "The Great Gatsby",
        author: "F. Scott Fitzgerald",
        price: 14.99,
        stock: 12,
        sellerId: sellerId,
        imageBase64: "",
        createdAt: Date.now(),
      },
      {
        id: randomUUID(),
        title: "To Kill a Mockingbird",
        author: "Harper Lee",
        price: 12.99,
        stock: 8,
        sellerId: sellerId,
        imageBase64: "",
        createdAt: Date.now(),
      },
      {
        id: randomUUID(),
        title: "1984",
        author: "George Orwell",
        price: 13.99,
        stock: 15,
        sellerId: sellerId,
        imageBase64: "",
        createdAt: Date.now(),
      },
      {
        id: randomUUID(),
        title: "Pride and Prejudice",
        author: "Jane Austen",
        price: 11.99,
        stock: 10,
        sellerId: sellerId,
        imageBase64: "",
        createdAt: Date.now(),
      },
      {
        id: randomUUID(),
        title: "The Catcher in the Rye",
        author: "J.D. Salinger",
        price: 15.99,
        stock: 6,
        sellerId: sellerId,
        imageBase64: "",
        createdAt: Date.now(),
      },
    ];

    books.forEach(book => this.books.set(book.id, book));
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id, createdAt: Date.now() };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    const updatedUser = { ...user, ...updates };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async getBook(id: string): Promise<Book | undefined> {
    return this.books.get(id);
  }

  async createBook(insertBook: InsertBook): Promise<Book> {
    const id = randomUUID();
    const book: Book = { ...insertBook, id, createdAt: Date.now() };
    this.books.set(id, book);
    return book;
  }

  async updateBook(id: string, updates: Partial<Book>): Promise<Book | undefined> {
    const book = this.books.get(id);
    if (!book) return undefined;
    const updatedBook = { ...book, ...updates };
    this.books.set(id, updatedBook);
    return updatedBook;
  }

  async deleteBook(id: string): Promise<boolean> {
    return this.books.delete(id);
  }

  async getAllBooks(): Promise<Book[]> {
    return Array.from(this.books.values());
  }

  async getBooksBySeller(sellerId: string): Promise<Book[]> {
    return Array.from(this.books.values()).filter(book => book.sellerId === sellerId);
  }

  async getOrder(id: string): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = randomUUID();
    const order: Order = { ...insertOrder, id, createdAt: Date.now() };
    this.orders.set(id, order);
    return order;
  }

  async getAllOrders(): Promise<Order[]> {
    return Array.from(this.orders.values());
  }

  async getOrdersByBuyer(buyerId: string): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.buyerId === buyerId);
  }
}

export const storage = new MemStorage();