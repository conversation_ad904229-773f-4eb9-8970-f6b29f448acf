import { z } from "zod";

// User Schema
export const userSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  passwordHash: z.string(),
  role: z.enum(["buyer", "seller", "admin"]),
  blocked: z.boolean().default(false),
  createdAt: z.number().default(() => Date.now()),
});

export const insertUserSchema = userSchema.omit({ id: true, createdAt: true });

// Book Schema
export const bookSchema = z.object({
  id: z.string(),
  title: z.string(),
  author: z.string(),
  price: z.number().positive(),
  stock: z.number().int().min(0),
  sellerId: z.string(),
  imageBase64: z.string().optional(),
  createdAt: z.number().default(() => Date.now()),
});

export const insertBookSchema = bookSchema.omit({ id: true, createdAt: true });

// Order Schema
export const orderItemSchema = z.object({
  bookId: z.string(),
  qty: z.number().int().positive(),
  priceAtPurchase: z.number().positive(),
});

export const orderSchema = z.object({
  id: z.string(),
  buyerId: z.string(),
  items: z.array(orderItemSchema),
  total: z.number().positive(),
  createdAt: z.number().default(() => Date.now()),
});

export const insertOrderSchema = orderSchema.omit({ id: true, createdAt: true });

// Cart Schema (for frontend use)
export const cartItemSchema = z.object({
  bookId: z.string(),
  quantity: z.number().int().positive(),
});

export const cartSchema = z.object({
  items: z.array(cartItemSchema),
  total: z.number().min(0),
});

// Criteria Flags Schema
export const criteriaFlagsSchema = z.object({
  criterion1: z.boolean().default(false), // Arrays
  criterion2: z.boolean().default(false), // User-defined objects
  criterion3: z.boolean().default(false), // Objects as data records
  criterion4: z.boolean().default(false), // Simple selection
  criterion5: z.boolean().default(false), // Complex selection
  criterion6: z.boolean().default(false), // Loops
  criterion7: z.boolean().default(false), // Nested loops
  criterion8: z.boolean().default(false), // User-defined methods
  criterion9: z.boolean().default(false), // User-defined methods with parameters
  criterion10: z.boolean().default(false), // User-defined methods with return values
  criterion11: z.boolean().default(false), // Sorting
  criterion12: z.boolean().default(false), // Searching
  criterion13: z.boolean().default(false), // File I/O
  criterion14: z.boolean().default(false), // Use of additional libraries
  criterion15: z.boolean().default(false), // Use of sentinels or flags
});

export type User = z.infer<typeof userSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Book = z.infer<typeof bookSchema>;
export type InsertBook = z.infer<typeof insertBookSchema>;
export type Order = z.infer<typeof orderSchema>;
export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type OrderItem = z.infer<typeof orderItemSchema>;
export type Cart = z.infer<typeof cartSchema>;
export type CartItem = z.infer<typeof cartItemSchema>;
export type CriteriaFlags = z.infer<typeof criteriaFlagsSchema>;