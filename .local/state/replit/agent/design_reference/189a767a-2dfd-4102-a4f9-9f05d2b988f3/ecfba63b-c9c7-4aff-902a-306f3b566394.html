<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookStore - Multi-Role Web Application</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
    tailwind.config = {
        theme: {
            extend: {
                borderRadius: {
                    lg: "var(--radius)",
                    md: "calc(var(--radius) - 2px)",
                    sm: "calc(var(--radius) - 4px)",
                },
                colors: {
                    background: "var(--background)",
                    foreground: "var(--foreground)",
                    card: {
                        DEFAULT: "var(--card)",
                        foreground: "var(--card-foreground)",
                    },
                    popover: {
                        DEFAULT: "var(--popover)",
                        foreground: "var(--popover-foreground)",
                    },
                    primary: {
                        DEFAULT: "var(--primary)",
                        foreground: "var(--primary-foreground)",
                    },
                    secondary: {
                        DEFAULT: "var(--secondary)",
                        foreground: "var(--secondary-foreground)",
                    },
                    muted: {
                        DEFAULT: "var(--muted)",
                        foreground: "var(--muted-foreground)",
                    },
                    accent: {
                        DEFAULT: "var(--accent)",
                        foreground: "var(--accent-foreground)",
                    },
                    destructive: {
                        DEFAULT: "var(--destructive)",
                        foreground: "var(--destructive-foreground)",
                    },
                    border: "var(--border)",
                    input: "var(--input)",
                    ring: "var(--ring)",
                    chart: {
                        "1": "var(--chart-1)",
                        "2": "var(--chart-2)",
                        "3": "var(--chart-3)",
                        "4": "var(--chart-4)",
                        "5": "var(--chart-5)",
                    }
                },
                fontFamily: {
                    sans: ["Inter", "system-ui", "sans-serif"],
                }
            },
        }
    };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --radius: 8px;
            --background: hsl(0 0% 100%);
            --foreground: hsl(222 84% 4.9%);
            --card: hsl(0 0% 100%);
            --card-foreground: hsl(222 84% 4.9%);
            --popover: hsl(0 0% 100%);
            --popover-foreground: hsl(222 84% 4.9%);
            --primary: hsl(221 83% 53%);
            --primary-foreground: hsl(210 40% 98%);
            --secondary: hsl(210 40% 96%);
            --secondary-foreground: hsl(222.2 84% 4.9%);
            --muted: hsl(210 40% 96%);
            --muted-foreground: hsl(215.4 16.3% 46.9%);
            --accent: hsl(210 40% 96%);
            --accent-foreground: hsl(222.2 84% 4.9%);
            --destructive: hsl(0 84.2% 60.2%);
            --destructive-foreground: hsl(210 40% 98%);
            --border: hsl(214.3 31.8% 91.4%);
            --input: hsl(214.3 31.8% 91.4%);
            --ring: hsl(221 83% 53%);
            --chart-1: hsl(12 76% 61%);
            --chart-2: hsl(173 58% 39%);
            --chart-3: hsl(197 37% 24%);
            --chart-4: hsl(43 74% 66%);
            --chart-5: hsl(27 87% 67%);
        }

        .dark {
            --background: hsl(222.2 84% 4.9%);
            --foreground: hsl(210 40% 98%);
            --card: hsl(222.2 84% 4.9%);
            --card-foreground: hsl(210 40% 98%);
            --popover: hsl(222.2 84% 4.9%);
            --popover-foreground: hsl(210 40% 98%);
            --primary: hsl(210 40% 98%);
            --primary-foreground: hsl(222.2 47.4% 11.2%);
            --secondary: hsl(217.2 32.6% 17.5%);
            --secondary-foreground: hsl(210 40% 98%);
            --muted: hsl(217.2 32.6% 17.5%);
            --muted-foreground: hsl(215 20.2% 65.1%);
            --accent: hsl(217.2 32.6% 17.5%);
            --accent-foreground: hsl(210 40% 98%);
            --destructive: hsl(0 62.8% 30.6%);
            --destructive-foreground: hsl(210 40% 98%);
            --border: hsl(217.2 32.6% 17.5%);
            --input: hsl(217.2 32.6% 17.5%);
            --ring: hsl(212.7 26.8% 83.9%);
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            background: #10B981;
            color: white;
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        @media (max-width: 768px) {
            .mobile-menu {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .mobile-menu.open {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="font-sans bg-background text-foreground min-h-screen">
    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Navigation Header -->
    <!-- @COMPONENT: Header [user, role, logout handler] -->
    <nav class="bg-card border-b border-border sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-book text-2xl text-primary mr-3"></i>
                        <h1 class="text-xl font-bold text-foreground">BookStore</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="hidden md:block text-sm text-muted-foreground" data-bind="user.name">John Doe</span>
                    <span class="hidden md:block px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full" data-bind="user.role">Admin</span>
                    <button class="text-muted-foreground hover:text-foreground" data-event="click:logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                    <button class="md:hidden text-muted-foreground" id="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    <!-- @END_COMPONENT: Header -->

    <!-- Main Application Container -->
    <div class="flex min-h-screen bg-background">
        <!-- Sidebar Navigation -->
        <!-- @COMPONENT: Sidebar [activeView, role-based navigation] -->
        <aside class="w-64 bg-card border-r border-border hidden md:block">
            <div class="h-full px-3 py-4">
                <ul class="space-y-2">
                    <li>
                        <button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg group active" data-view="dashboard">
                            <i class="fas fa-home w-5 h-5"></i>
                            <span class="ml-3">Dashboard</span>
                        </button>
                    </li>
                    <li>
                        <button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg group" data-view="books">
                            <i class="fas fa-book w-5 h-5"></i>
                            <span class="ml-3">Books</span>
                        </button>
                    </li>
                    <li>
                        <button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg group" data-view="users">
                            <i class="fas fa-users w-5 h-5"></i>
                            <span class="ml-3">Users</span>
                        </button>
                    </li>
                    <li>
                        <button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg group" data-view="orders">
                            <i class="fas fa-shopping-cart w-5 h-5"></i>
                            <span class="ml-3">Orders</span>
                        </button>
                    </li>
                    <li>
                        <button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg group" data-view="analytics">
                            <i class="fas fa-chart-bar w-5 h-5"></i>
                            <span class="ml-3">Analytics</span>
                        </button>
                    </li>
                </ul>
            </div>
        </aside>
        <!-- @END_COMPONENT: Sidebar -->

        <!-- Mobile Sidebar -->
        <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform mobile-menu md:hidden" id="mobile-sidebar">
            <div class="h-full px-3 py-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">Menu</h2>
                    <button id="close-mobile-menu" class="text-muted-foreground">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <ul class="space-y-2">
                    <li><button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg" data-view="dashboard"><i class="fas fa-home w-5 h-5"></i><span class="ml-3">Dashboard</span></button></li>
                    <li><button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg" data-view="books"><i class="fas fa-book w-5 h-5"></i><span class="ml-3">Books</span></button></li>
                    <li><button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg" data-view="users"><i class="fas fa-users w-5 h-5"></i><span class="ml-3">Users</span></button></li>
                    <li><button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg" data-view="orders"><i class="fas fa-shopping-cart w-5 h-5"></i><span class="ml-3">Orders</span></button></li>
                    <li><button class="flex items-center w-full p-2 text-foreground hover:bg-accent rounded-lg" data-view="analytics"><i class="fas fa-chart-bar w-5 h-5"></i><span class="ml-3">Analytics</span></button></li>
                </ul>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Admin Dashboard View -->
            <!-- @COMPONENT: AdminDashboard -->
            <div id="admin-dashboard" class="p-6">
                <!-- Dashboard Header -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-foreground mb-2">Admin Dashboard</h2>
                    <p class="text-muted-foreground">Manage your bookstore operations</p>
                </div>

                <!-- Quick Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <div class="flex items-center">
                            <div class="p-2 bg-primary/10 rounded-lg">
                                <i class="fas fa-book text-primary text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-muted-foreground">Total Books</p>
                                <p class="text-2xl font-bold" data-bind="stats.totalBooks">156</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i class="fas fa-users text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-muted-foreground">Active Users</p>
                                <p class="text-2xl font-bold" data-bind="stats.activeUsers">1,234</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i class="fas fa-shopping-cart text-yellow-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-muted-foreground">Total Orders</p>
                                <p class="text-2xl font-bold" data-bind="stats.totalOrders">89</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-muted-foreground">Revenue</p>
                                <p class="text-2xl font-bold" data-bind="stats.revenue">$12,456</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Chart -->
                <!-- @FUNCTIONALITY: Chart.js integration for admin analytics -->
                <div class="bg-card p-6 rounded-lg border border-border mb-8">
                    <h3 class="text-lg font-semibold mb-4">Sales Analytics</h3>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>

                <!-- Recent Activity & Management Tables -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Orders -->
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <h3 class="text-lg font-semibold mb-4">Recent Orders</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-border">
                                        <th class="text-left py-2 font-medium">Order ID</th>
                                        <th class="text-left py-2 font-medium">Customer</th>
                                        <th class="text-left py-2 font-medium">Total</th>
                                        <th class="text-left py-2 font-medium">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- @MAP: recentOrders.map(order => ( -->
                                    <tr class="border-b border-border">
                                        <td class="py-2 text-primary" data-mock="true">#ORD-001</td>
                                        <td class="py-2" data-mock="true">Alice Johnson</td>
                                        <td class="py-2" data-mock="true">$29.99</td>
                                        <td class="py-2"><span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs" data-mock="true">Completed</span></td>
                                    </tr>
                                    <tr class="border-b border-border">
                                        <td class="py-2 text-primary" data-mock="true">#ORD-002</td>
                                        <td class="py-2" data-mock="true">Bob Smith</td>
                                        <td class="py-2" data-mock="true">$45.50</td>
                                        <td class="py-2"><span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs" data-mock="true">Processing</span></td>
                                    </tr>
                                    <tr class="border-b border-border">
                                        <td class="py-2 text-primary" data-mock="true">#ORD-003</td>
                                        <td class="py-2" data-mock="true">Carol Davis</td>
                                        <td class="py-2" data-mock="true">$78.25</td>
                                        <td class="py-2"><span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs" data-mock="true">Completed</span></td>
                                    </tr>
                                    <!-- @END_MAP )) -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <h3 class="text-lg font-semibold mb-4">User Management</h3>
                        <div class="space-y-3">
                            <!-- @MAP: users.map(user => ( -->
                            <div class="flex items-center justify-between p-3 bg-muted rounded-lg" data-mock="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">A</div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium" data-mock="true">Alice Johnson</p>
                                        <p class="text-xs text-muted-foreground" data-mock="true">Buyer • <EMAIL></p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200" data-event="click:blockUser">Block</button>
                                    <button class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200" data-event="click:deleteUser">Delete</button>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-muted rounded-lg" data-mock="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">B</div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium" data-mock="true">Bob Smith</p>
                                        <p class="text-xs text-muted-foreground" data-mock="true">Seller • <EMAIL></p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200" data-event="click:blockUser">Block</button>
                                    <button class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200" data-event="click:deleteUser">Delete</button>
                                </div>
                            </div>
                            <!-- @END_MAP )) -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: AdminDashboard -->

            <!-- Buyer Dashboard View -->
            <!-- @COMPONENT: BuyerDashboard -->
            <div id="buyer-dashboard" class="p-6 hidden">
                <!-- Search and Filter Bar -->
                <div class="mb-8">
                    <div class="flex flex-col md:flex-row gap-4 mb-6">
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-search absolute left-3 top-3 text-muted-foreground"></i>
                                <input 
                                    type="text" 
                                    placeholder="Search books by title or author..." 
                                    class="w-full pl-10 pr-4 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring"
                                    data-event="input:searchBooks"
                                >
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <select class="px-4 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" data-event="change:sortBooks">
                                <option value="title-asc">Title A-Z</option>
                                <option value="title-desc">Title Z-A</option>
                                <option value="price-asc">Price Low-High</option>
                                <option value="price-desc">Price High-Low</option>
                            </select>
                            <button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 flex items-center" data-event="click:toggleCart">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                Cart (<span data-bind="cart.itemCount">3</span>)
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Book Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                    <!-- @MAP: books.map(book => ( -->
                    <!-- @COMPONENT: BookCard [book, onAddToCart] -->
                    <div class="bg-card p-4 rounded-lg border border-border hover:shadow-lg transition-shadow" data-mock="true">
                        <!-- Modern minimalist book cover design -->
                        <img src="https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=400" alt="The Great Gatsby book cover" class="w-full h-48 object-cover rounded-md mb-4" data-bind="book.imageBase64" />
                        <h3 class="font-semibold text-lg mb-2" data-bind="book.title" data-mock="true">The Great Gatsby</h3>
                        <p class="text-muted-foreground text-sm mb-2" data-bind="book.author" data-mock="true">F. Scott Fitzgerald</p>
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xl font-bold text-primary" data-bind="book.price" data-mock="true">$14.99</span>
                            <span class="text-sm text-muted-foreground" data-bind="book.stock" data-mock="true">12 in stock</span>
                        </div>
                        <button class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90 transition-colors" data-event="click:addToCart">
                            Add to Cart
                        </button>
                    </div>
                    <!-- @END_COMPONENT: BookCard -->
                    
                    <div class="bg-card p-4 rounded-lg border border-border hover:shadow-lg transition-shadow" data-mock="true">
                        <!-- Classic literature book cover -->
                        <img src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=400" alt="To Kill a Mockingbird book cover" class="w-full h-48 object-cover rounded-md mb-4" />
                        <h3 class="font-semibold text-lg mb-2" data-mock="true">To Kill a Mockingbird</h3>
                        <p class="text-muted-foreground text-sm mb-2" data-mock="true">Harper Lee</p>
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xl font-bold text-primary" data-mock="true">$12.99</span>
                            <span class="text-sm text-muted-foreground" data-mock="true">8 in stock</span>
                        </div>
                        <button class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90 transition-colors">
                            Add to Cart
                        </button>
                    </div>

                    <div class="bg-card p-4 rounded-lg border border-border hover:shadow-lg transition-shadow" data-mock="true">
                        <!-- Science fiction book cover -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=400" alt="Dune book cover with desert landscape" class="w-full h-48 object-cover rounded-md mb-4" />
                        <h3 class="font-semibold text-lg mb-2" data-mock="true">Dune</h3>
                        <p class="text-muted-foreground text-sm mb-2" data-mock="true">Frank Herbert</p>
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xl font-bold text-primary" data-mock="true">$18.99</span>
                            <span class="text-sm text-muted-foreground" data-mock="true">15 in stock</span>
                        </div>
                        <button class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90 transition-colors">
                            Add to Cart
                        </button>
                    </div>

                    <div class="bg-card p-4 rounded-lg border border-border hover:shadow-lg transition-shadow" data-mock="true">
                        <!-- Mystery novel book cover -->
                        <img src="https://pixabay.com/get/g4b07232c3db3b1eac6053241f25080b409a59a83482d8221139ac06498b04d174c1984ee4bf50620f40ba664783f5e0d1ff57ca336182ecf673d2258cb234a2d_1280.jpg" alt="Gone Girl mystery book cover with dark atmosphere" class="w-full h-48 object-cover rounded-md mb-4" />
                        <h3 class="font-semibold text-lg mb-2" data-mock="true">Gone Girl</h3>
                        <p class="text-muted-foreground text-sm mb-2" data-mock="true">Gillian Flynn</p>
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xl font-bold text-primary" data-mock="true">$16.99</span>
                            <span class="text-sm text-muted-foreground" data-mock="true">6 in stock</span>
                        </div>
                        <button class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90 transition-colors">
                            Add to Cart
                        </button>
                    </div>
                    <!-- @END_MAP )) -->
                </div>

                <!-- Shopping Cart Sidebar -->
                <!-- @COMPONENT: CartSidebar [cartItems, onUpdateQuantity, onRemoveItem, onCheckout] -->
                <div id="cart-sidebar" class="fixed inset-y-0 right-0 w-80 bg-card border-l border-border transform translate-x-full transition-transform z-50">
                    <div class="h-full flex flex-col">
                        <div class="p-4 border-b border-border">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold">Shopping Cart</h3>
                                <button class="text-muted-foreground hover:text-foreground" data-event="click:closeCart">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-y-auto p-4">
                            <!-- Cart Items -->
                            <!-- @MAP: cartItems.map(item => ( -->
                            <div class="flex items-center space-x-3 mb-4 p-3 bg-muted rounded-lg" data-mock="true">
                                <!-- Book thumbnail from cart -->
                                <img src="https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&h=80" alt="The Great Gatsby thumbnail" class="w-12 h-16 object-cover rounded" />
                                <div class="flex-1">
                                    <h4 class="font-medium text-sm" data-mock="true">The Great Gatsby</h4>
                                    <p class="text-xs text-muted-foreground" data-mock="true">$14.99</p>
                                    <div class="flex items-center mt-1">
                                        <button class="text-xs px-1" data-event="click:decreaseQuantity">-</button>
                                        <span class="mx-2 text-xs" data-bind="item.quantity" data-mock="true">2</span>
                                        <button class="text-xs px-1" data-event="click:increaseQuantity">+</button>
                                        <button class="ml-2 text-xs text-red-600 hover:text-red-800" data-event="click:removeItem">Remove</button>
                                    </div>
                                </div>
                            </div>
                            <!-- @END_MAP )) -->
                        </div>
                        <div class="p-4 border-t border-border">
                            <div class="flex justify-between items-center mb-4">
                                <span class="font-semibold">Total:</span>
                                <span class="font-bold text-lg" data-bind="cart.total" data-mock="true">$44.97</span>
                            </div>
                            <button class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90" data-event="click:checkout">
                                Proceed to Checkout
                            </button>
                        </div>
                    </div>
                </div>
                <!-- @END_COMPONENT: CartSidebar -->
            </div>
            <!-- @END_COMPONENT: BuyerDashboard -->

            <!-- Seller Dashboard View -->
            <!-- @COMPONENT: SellerDashboard -->
            <div id="seller-dashboard" class="p-6 hidden">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-foreground mb-2">Seller Dashboard</h2>
                    <p class="text-muted-foreground">Manage your book inventory</p>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <!-- Add New Book Form -->
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <h3 class="text-lg font-semibold mb-4">Add New Book</h3>
                        <!-- @COMPONENT: BookForm [onSubmit, initialData] -->
                        <form class="space-y-4" data-event="submit:addBook">
                            <div>
                                <label class="block text-sm font-medium mb-1">Title</label>
                                <input type="text" name="title" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Author</label>
                                <input type="text" name="author" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-1">Price</label>
                                    <input type="number" name="price" step="0.01" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-1">Stock</label>
                                    <input type="number" name="stock" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Book Cover Image</label>
                                <input type="file" accept="image/*" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" data-event="change:handleImageUpload">
                                <div class="mt-2 p-2 border-2 border-dashed border-border rounded-lg text-center text-muted-foreground hidden" id="image-preview">
                                    <!-- Image preview will appear here -->
                                    <img class="max-w-full h-32 mx-auto object-cover rounded" alt="Book cover preview" />
                                </div>
                            </div>
                            <button type="submit" class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90">
                                Add Book
                            </button>
                        </form>
                        <!-- @END_COMPONENT: BookForm -->
                    </div>

                    <!-- Seller Stats -->
                    <div class="bg-card p-6 rounded-lg border border-border">
                        <h3 class="text-lg font-semibold mb-4">Your Statistics</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">Total Books:</span>
                                <span class="font-semibold" data-bind="sellerStats.totalBooks" data-mock="true">23</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">Books Sold:</span>
                                <span class="font-semibold" data-bind="sellerStats.booksSold" data-mock="true">47</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">Total Revenue:</span>
                                <span class="font-semibold text-green-600" data-bind="sellerStats.revenue" data-mock="true">$1,234.56</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">Avg. Rating:</span>
                                <span class="font-semibold" data-bind="sellerStats.avgRating" data-mock="true">4.3 ⭐</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Books List -->
                <div class="bg-card p-6 rounded-lg border border-border">
                    <h3 class="text-lg font-semibold mb-4">My Books</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-border">
                                    <th class="text-left py-2 font-medium">Cover</th>
                                    <th class="text-left py-2 font-medium">Title</th>
                                    <th class="text-left py-2 font-medium">Author</th>
                                    <th class="text-left py-2 font-medium">Price</th>
                                    <th class="text-left py-2 font-medium">Stock</th>
                                    <th class="text-left py-2 font-medium">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- @MAP: sellerBooks.map(book => ( -->
                                <tr class="border-b border-border">
                                    <td class="py-2">
                                        <!-- Seller's book cover thumbnail -->
                                        <img src="https://images.unsplash.com/photo-1592496431122-2349e0fbc666?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=60" alt="Programming book cover thumbnail" class="w-8 h-12 object-cover rounded" data-bind="book.imageBase64" />
                                    </td>
                                    <td class="py-2 font-medium" data-bind="book.title" data-mock="true">JavaScript Fundamentals</td>
                                    <td class="py-2" data-bind="book.author" data-mock="true">John Doe</td>
                                    <td class="py-2" data-bind="book.price" data-mock="true">$24.99</td>
                                    <td class="py-2" data-bind="book.stock" data-mock="true">15</td>
                                    <td class="py-2">
                                        <div class="flex space-x-2">
                                            <button class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs hover:bg-yellow-200" data-event="click:editBook">Edit</button>
                                            <button class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200" data-event="click:deleteBook">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-b border-border">
                                    <td class="py-2">
                                        <!-- Technical book cover thumbnail -->
                                        <img src="https://images.unsplash.com/photo-1516979187457-637abb4f9353?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=60" alt="Database design book thumbnail" class="w-8 h-12 object-cover rounded" />
                                    </td>
                                    <td class="py-2 font-medium" data-mock="true">Database Design Patterns</td>
                                    <td class="py-2" data-mock="true">John Doe</td>
                                    <td class="py-2" data-mock="true">$32.99</td>
                                    <td class="py-2" data-mock="true">8</td>
                                    <td class="py-2">
                                        <div class="flex space-x-2">
                                            <button class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs hover:bg-yellow-200">Edit</button>
                                            <button class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs hover:bg-red-200">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- @END_MAP )) -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: SellerDashboard -->

            <!-- Authentication View -->
            <!-- @COMPONENT: AuthForm [mode: login/register, onSubmit] -->
            <div id="auth-view" class="min-h-screen flex items-center justify-center bg-muted/20 hidden">
                <div class="max-w-md w-full bg-card p-8 rounded-lg border border-border shadow-lg">
                    <div class="text-center mb-8">
                        <i class="fas fa-book text-4xl text-primary mb-4"></i>
                        <h1 class="text-2xl font-bold text-foreground">BookStore</h1>
                        <p class="text-muted-foreground">Welcome to your digital bookstore</p>
                    </div>

                    <!-- Login Form -->
                    <div id="login-form">
                        <h2 class="text-xl font-semibold mb-6 text-center">Login</h2>
                        <form class="space-y-4" data-event="submit:loginUser">
                            <div>
                                <label class="block text-sm font-medium mb-1">Email</label>
                                <input type="email" name="email" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Password</label>
                                <input type="password" name="password" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <button type="submit" class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90">
                                Login
                            </button>
                        </form>
                        <p class="text-center text-sm text-muted-foreground mt-4">
                            Don't have an account? 
                            <button class="text-primary hover:underline" data-event="click:switchToRegister">Register here</button>
                        </p>
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p class="text-xs text-yellow-800">
                                <strong>Demo Admin:</strong> <EMAIL> / Admin@123
                            </p>
                        </div>
                    </div>

                    <!-- Register Form -->
                    <div id="register-form" class="hidden">
                        <h2 class="text-xl font-semibold mb-6 text-center">Register</h2>
                        <form class="space-y-4" data-event="submit:registerUser">
                            <div>
                                <label class="block text-sm font-medium mb-1">Full Name</label>
                                <input type="text" name="name" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Email</label>
                                <input type="email" name="email" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Password</label>
                                <input type="password" name="password" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Role</label>
                                <select name="role" class="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring" required data-event="change:roleSelection">
                                    <option value="">Select your role</option>
                                    <option value="buyer">Buyer - Browse and purchase books</option>
                                    <option value="seller">Seller - Sell your own books</option>
                                </select>
                            </div>
                            <button type="submit" class="w-full bg-primary text-primary-foreground py-2 rounded-lg hover:bg-primary/90">
                                Register
                            </button>
                        </form>
                        <p class="text-center text-sm text-muted-foreground mt-4">
                            Already have an account? 
                            <button class="text-primary hover:underline" data-event="click:switchToLogin">Login here</button>
                        </p>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: AuthForm -->
        </main>
    </div>

    <!-- Criteria Progress Tracker -->
    <!-- @COMPONENT: CriteriaTracker [criteriaFlags] -->
    <div class="fixed bottom-4 left-4 bg-card p-4 rounded-lg border border-border shadow-lg max-w-sm z-40">
        <h4 class="text-sm font-semibold mb-3 flex items-center">
            <i class="fas fa-tasks mr-2 text-primary"></i>
            Criteria Progress
        </h4>
        <div class="grid grid-cols-5 gap-1">
            <!-- @MAP: Array.from({length: 15}).map((_, i) => ( -->
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="1" data-mock="true">1</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="2" data-mock="true">2</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="3" data-mock="true">3</div>
            <div class="w-6 h-6 rounded border-2 border-green-200 bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium criteria-chip completed" data-criterion="4" data-mock="true">4</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="5" data-mock="true">5</div>
            <div class="w-6 h-6 rounded border-2 border-green-200 bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium criteria-chip completed" data-criterion="6" data-mock="true">6</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="7" data-mock="true">7</div>
            <div class="w-6 h-6 rounded border-2 border-green-200 bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium criteria-chip completed" data-criterion="8" data-mock="true">8</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="9" data-mock="true">9</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="10" data-mock="true">10</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="11" data-mock="true">11</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="12" data-mock="true">12</div>
            <div class="w-6 h-6 rounded border-2 border-green-200 bg-green-100 text-green-800 flex items-center justify-center text-xs font-medium criteria-chip completed" data-criterion="13" data-mock="true">13</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="14" data-mock="true">14</div>
            <div class="w-6 h-6 rounded border-2 border-border bg-muted flex items-center justify-center text-xs font-medium criteria-chip" data-criterion="15" data-mock="true">15</div>
            <!-- @END_MAP )) -->
        </div>
        <div class="mt-2 text-xs text-muted-foreground">
            <span data-bind="completedCriteria" data-mock="true">4</span>/15 Completed
        </div>
    </div>
    <!-- @END_COMPONENT: CriteriaTracker -->

    <script>
        (function() {
            // TODO: Implement localStorage service functions
            // TODO: Implement authentication system
            // TODO: Implement role-based routing
            // TODO: Implement CRUD operations for books
            // TODO: Implement shopping cart functionality
            // TODO: Implement criteria tracking system
            
            // Demo Chart.js Implementation
            const ctx = document.getElementById('salesChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Monthly Sales',
                            data: [1200, 1900, 3000, 2500, 2200, 3200],
                            borderColor: 'hsl(221 83% 53%)',
                            backgroundColor: 'hsla(221 83% 53% / 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'hsl(214.3 31.8% 91.4%)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Mobile menu functionality
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileSidebar = document.getElementById('mobile-sidebar');
            const closeMobileMenu = document.getElementById('close-mobile-menu');

            mobileMenuBtn?.addEventListener('click', () => {
                mobileSidebar.classList.add('open');
            });

            closeMobileMenu?.addEventListener('click', () => {
                mobileSidebar.classList.remove('open');
            });

            // Toast notification system
            function showToast(message, type = 'success') {
                const container = document.getElementById('toast-container');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                `;
                container.appendChild(toast);
                
                setTimeout(() => toast.classList.add('show'), 100);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => container.removeChild(toast), 300);
                }, 3000);
            }

            // Demo toast triggers
            setTimeout(() => showToast('✅ Criteria 4 – Simple Selection – Complete'), 2000);
            setTimeout(() => showToast('✅ Criteria 6 – Loops – Complete'), 4000);
            setTimeout(() => showToast('✅ Criteria 8 – User-defined Methods – Complete'), 6000);
            setTimeout(() => showToast('✅ Criteria 13 – File I/O – Complete'), 8000);

            // TODO: Implement criteria completion tracking
            // TODO: Implement "all done" celebration toast
            // TODO: Implement image upload and base64 conversion
            // TODO: Implement search and sort functionality
            // TODO: Implement checkout process with stock validation

        })();
    </script>
</body>
</html>